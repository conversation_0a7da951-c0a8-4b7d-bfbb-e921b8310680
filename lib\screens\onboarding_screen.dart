import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/supabase_service.dart';
import '../widgets/coffee_wave_progress_indicator.dart';
import '../widgets/auth_gate.dart';

/// Multi-step onboarding screen for new users
/// 
/// This screen guides users through setting up their profile information
/// when they first log in or haven't completed their profile.
/// Features coffee theme consistency and smooth animations.
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  // Page navigation
  final PageController _pageController = PageController();
  int _currentPage = 0;
  
  // Form controllers and state
  final TextEditingController _nameController = TextEditingController();
  File? _profileImageFile;
  final ImagePicker _imagePicker = ImagePicker();

  // Coffee type preferences (multiple selection)
  final List<String> _favoriteCoffeeTypes = [];
  static const List<String> _coffeeTypeOptions = [
    'Espresso', 'Latte', 'Filtre Kahve', 'Türk Kahvesi',
    'Cappuccino', 'Americano', 'Macchiato', 'Mocha'
  ];

  // Roast level preference (single selection)
  String? _preferredRoastLevel;
  static const List<String> _roastLevelOptions = [
    'Açık Kavrulmuş', 'Orta Kavrulmuş', 'Koyu Kavrulmuş'
  ];

  // Milk preferences (multiple selection)
  final List<String> _milkPreferences = [];
  static const List<String> _milkOptions = [
    'Sütsüz', 'Normal Süt', 'Laktozsuz Süt', 'Badem Sütü', 'Soya Sütü', 'Yulaf Sütü'
  ];

  // Sweetener preference (boolean)
  bool _prefersSweetened = false;

  // Loading state for save operation
  bool _isSaving = false;

  // Supabase service instance
  final SupabaseService _supabaseService = SupabaseService();

  // Coffee theme colors
  static const Color _coffeeColor = Color(0xFF6F4E37);
  static const Color _creamColor = Color(0xFFF5F5DC);
  
  @override
  void dispose() {
    _pageController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  /// Handles image selection from gallery or camera
  Future<void> _pickImage() async {
    try {
      // Show modal bottom sheet with options
      final ImageSource? source = await showModalBottomSheet<ImageSource>(
        context: context,
        backgroundColor: Theme.of(context).colorScheme.surface,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (BuildContext context) {
          return SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Profil Fotoğrafı Seç',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildImageSourceOption(
                        context,
                        icon: Icons.photo_library,
                        label: 'Galeri',
                        source: ImageSource.gallery,
                      ),
                      _buildImageSourceOption(
                        context,
                        icon: Icons.camera_alt,
                        label: 'Kamera',
                        source: ImageSource.camera,
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          );
        },
      );

      if (source != null) {
        await _selectImageFromSource(source);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fotoğraf seçilirken hata oluştu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Builds image source option widget for bottom sheet
  Widget _buildImageSourceOption(
    BuildContext context, {
    required IconData icon,
    required String label,
    required ImageSource source,
  }) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(source),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: _coffeeColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _coffeeColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 32,
              color: _coffeeColor,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: _coffeeColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Selects image from the specified source with permission handling
  Future<void> _selectImageFromSource(ImageSource source) async {
    try {
      // Check and request permissions
      Permission permission = source == ImageSource.camera 
          ? Permission.camera 
          : Permission.photos;
      
      PermissionStatus status = await permission.status;
      
      if (status.isDenied) {
        status = await permission.request();
      }
      
      if (status.isPermanentlyDenied) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                source == ImageSource.camera 
                    ? 'Kamera izni gerekli. Lütfen ayarlardan izin verin.'
                    : 'Galeri izni gerekli. Lütfen ayarlardan izin verin.',
              ),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'Ayarlar',
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
        return;
      }
      
      if (status.isGranted) {
        final XFile? pickedFile = await _imagePicker.pickImage(
          source: source,
          maxWidth: 512,
          maxHeight: 512,
          imageQuality: 80,
        );
        
        if (pickedFile != null) {
          setState(() {
            _profileImageFile = File(pickedFile.path);
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fotoğraf seçilirken hata oluştu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Saves user profile and completes onboarding
  ///
  /// This method handles the complete save process including:
  /// 1. Image upload (if profile image is selected)
  /// 2. Coffee preferences compilation
  /// 3. Profile data saving to Supabase
  /// 4. Navigation to main app on success
  Future<void> _saveProfileAndComplete() async {
    try {
      setState(() {
        _isSaving = true;
      });

      // Get current user ID
      final userId = _supabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('Kullanıcı bilgisi bulunamadı. Lütfen tekrar giriş yapın.');
      }

      debugPrint('OnboardingScreen: Starting profile save for user: $userId');

      // Step 1: Upload profile image if selected
      String? avatarUrl;
      if (_profileImageFile != null) {
        debugPrint('OnboardingScreen: Uploading profile image...');

        // Convert File to XFile for upload
        final XFile imageFile = XFile(_profileImageFile!.path);
        avatarUrl = await _supabaseService.uploadProfileImage(userId, imageFile);

        if (avatarUrl != null) {
          debugPrint('OnboardingScreen: Profile image uploaded successfully');
        } else {
          debugPrint('OnboardingScreen: Profile image upload failed, continuing without image');
        }
      }

      // Step 2: Compile coffee preferences
      final Map<String, dynamic> coffeePreferences = {
        'favorite_types': _favoriteCoffeeTypes,
        'roast_level': _preferredRoastLevel,
        'milk_options': _milkPreferences,
        'sweetened': _prefersSweetened,
      };

      debugPrint('OnboardingScreen: Coffee preferences compiled: $coffeePreferences');

      // Step 3: Save complete profile to Supabase
      await _supabaseService.updateUserProfile(
        userId: userId,
        fullName: _nameController.text.trim(),
        avatarUrl: avatarUrl,
        coffeePreferences: coffeePreferences,
        onboardingCompleted: true,
      );

      debugPrint('OnboardingScreen: Profile saved successfully');

      // Step 4: Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('Profil başarıyla kaydedildi!'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );

        // Small delay to show success message
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Step 5: Trigger AuthGate refresh to navigate to main app
      debugPrint('OnboardingScreen: Triggering navigation to main app');
      triggerOnboardingCompletion();

    } catch (e) {
      debugPrint('OnboardingScreen: Error saving profile: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    e.toString().replaceAll('Exception: ', ''),
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Tekrar Dene',
              textColor: Colors.white,
              onPressed: () => _saveProfileAndComplete(),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// Navigates to the next page or completes onboarding
  void _nextPage() {
    if (_currentPage < 1) { // Two pages total (0 and 1)
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // On final step, save profile and complete onboarding
      _saveProfileAndComplete();
    }
  }

  /// Navigates to the previous page
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Validates the current step
  bool _isCurrentStepValid() {
    switch (_currentPage) {
      case 0:
        return _nameController.text.trim().isNotEmpty;
      case 1:
        return _favoriteCoffeeTypes.isNotEmpty;
      default:
        return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _creamColor.withValues(alpha: 0.1),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator
            Container(
              padding: const EdgeInsets.all(16),
              child: LinearProgressIndicator(
                value: (_currentPage + 1) / 2, // Two steps total
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(_coffeeColor),
              ),
            ),
            
            // Main content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                children: [
                  _buildNameAndPictureStep(),
                  _buildCoffeePreferencesStep(),
                ],
              ),
            ),
            
            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  /// Builds the first onboarding step (name and picture)
  Widget _buildNameAndPictureStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Welcome title
          Text(
            'Hoş Geldin!',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _coffeeColor,
            ),
          ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),
          
          const SizedBox(height: 8),
          
          Text(
            'Profilini oluşturalım',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),
          
          const SizedBox(height: 40),
          
          // Profile picture section
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _coffeeColor.withValues(alpha: 0.1),
                border: Border.all(
                  color: _coffeeColor.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: _profileImageFile != null
                  ? ClipOval(
                      child: Image.file(
                        _profileImageFile!,
                        fit: BoxFit.cover,
                        width: 120,
                        height: 120,
                      ),
                    )
                  : Icon(
                      Icons.person_add_alt_1,
                      size: 48,
                      color: _coffeeColor,
                    ),
            ),
          ).animate(delay: 400.ms).fadeIn(duration: 600.ms).scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),
          
          const SizedBox(height: 16),
          
          Text(
            'Profil fotoğrafı ekle',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: _coffeeColor,
            ),
          ).animate(delay: 600.ms).fadeIn(duration: 600.ms),
          
          const SizedBox(height: 40),
          
          // Name input field
          TextField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: 'Adın Soyadın',
              hintText: 'Örn: Ahmet Yılmaz',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: _coffeeColor, width: 2),
              ),
              prefixIcon: Icon(Icons.person, color: _coffeeColor),
            ),
            textCapitalization: TextCapitalization.words,
            onChanged: (value) {
              setState(() {}); // Trigger rebuild to update button state
            },
          ).animate(delay: 800.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),
        ],
      ),
    );
  }

  /// Builds the second onboarding step (coffee preferences)
  Widget _buildCoffeePreferencesStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title section
          Center(
            child: Column(
              children: [
                Text(
                  'Kahve Zevklerini Keşfedelim!',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _coffeeColor,
                  ),
                  textAlign: TextAlign.center,
                ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),

                const SizedBox(height: 8),

                Text(
                  'Damak tadını bize anlat',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Coffee types selection
          Text(
            'En Sevdiğin Kahve Türleri Hangileri?',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _coffeeColor,
            ),
          ).animate(delay: 400.ms).fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),

          const SizedBox(height: 16),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _coffeeTypeOptions.asMap().entries.map((entry) {
              final index = entry.key;
              final coffeeType = entry.value;
              final isSelected = _favoriteCoffeeTypes.contains(coffeeType);

              return ChoiceChip(
                label: Text(coffeeType),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _favoriteCoffeeTypes.add(coffeeType);
                    } else {
                      _favoriteCoffeeTypes.remove(coffeeType);
                    }
                  });
                },
                selectedColor: _coffeeColor.withValues(alpha: 0.2),
                checkmarkColor: _coffeeColor,
                labelStyle: TextStyle(
                  color: isSelected ? _coffeeColor : Colors.grey[700],
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
                side: BorderSide(
                  color: isSelected ? _coffeeColor : Colors.grey[300]!,
                  width: 1,
                ),
              ).animate(delay: (600 + index * 100).ms)
                .fadeIn(duration: 600.ms)
                .slideX(begin: 0.3, end: 0);
            }).toList(),
          ),

          const SizedBox(height: 32),

          // Roast level selection
          Text(
            'Hangi Kavurma Derecesini Tercih Edersin?',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _coffeeColor,
            ),
          ).animate(delay: 800.ms).fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),

          const SizedBox(height: 16),

          SegmentedButton<String>(
            segments: _roastLevelOptions.map((level) {
              return ButtonSegment<String>(
                value: level,
                label: Text(level),
              );
            }).toList(),
            selected: _preferredRoastLevel != null ? {_preferredRoastLevel!} : {},
            emptySelectionAllowed: true,
            onSelectionChanged: (Set<String> newSelection) {
              setState(() {
                _preferredRoastLevel = newSelection.isNotEmpty ? newSelection.first : null;
              });
            },
            style: SegmentedButton.styleFrom(
              selectedBackgroundColor: _coffeeColor.withValues(alpha: 0.2),
              selectedForegroundColor: _coffeeColor,
              side: BorderSide(color: _coffeeColor.withValues(alpha: 0.3)),
            ),
          ).animate(delay: 1000.ms).fadeIn(duration: 600.ms).slideX(begin: 0.3, end: 0),

          const SizedBox(height: 32),

          // Milk preferences selection
          Text(
            'Kahveni Nasıl Alırsın?',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _coffeeColor,
            ),
          ).animate(delay: 1200.ms).fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),

          const SizedBox(height: 16),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _milkOptions.asMap().entries.map((entry) {
              final index = entry.key;
              final milkOption = entry.value;
              final isSelected = _milkPreferences.contains(milkOption);

              return ChoiceChip(
                label: Text(milkOption),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _milkPreferences.add(milkOption);
                    } else {
                      _milkPreferences.remove(milkOption);
                    }
                  });
                },
                selectedColor: _coffeeColor.withValues(alpha: 0.2),
                checkmarkColor: _coffeeColor,
                labelStyle: TextStyle(
                  color: isSelected ? _coffeeColor : Colors.grey[700],
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
                side: BorderSide(
                  color: isSelected ? _coffeeColor : Colors.grey[300]!,
                  width: 1,
                ),
              ).animate(delay: (1400 + index * 100).ms)
                .fadeIn(duration: 600.ms)
                .slideX(begin: 0.3, end: 0);
            }).toList(),
          ),

          const SizedBox(height: 32),

          // Sweetener preference
          Text(
            'Şeker Kullanır mısın?',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: _coffeeColor,
            ),
          ).animate(delay: 1600.ms).fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),

          const SizedBox(height: 16),

          Container(
            decoration: BoxDecoration(
              color: _coffeeColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _coffeeColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: SwitchListTile(
              title: Text(
                _prefersSweetened ? 'Evet, şeker kullanırım' : 'Hayır, şeker kullanmam',
                style: TextStyle(
                  color: _coffeeColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              value: _prefersSweetened,
              onChanged: (value) {
                setState(() {
                  _prefersSweetened = value;
                });
              },
              activeColor: _coffeeColor,
              secondary: Icon(
                _prefersSweetened ? Icons.add_circle : Icons.remove_circle,
                color: _coffeeColor,
              ),
            ),
          ).animate(delay: 1800.ms).fadeIn(duration: 600.ms).slideX(begin: 0.3, end: 0),
        ],
      ),
    );
  }

  /// Builds navigation buttons
  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Previous button (hidden for first step)
          if (_currentPage > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _isSaving ? null : _previousPage,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: _isSaving ? Colors.grey : _coffeeColor),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Geri',
                  style: TextStyle(color: _isSaving ? Colors.grey : _coffeeColor),
                ),
              ),
            ),
          
          if (_currentPage > 0) const SizedBox(width: 16),
          
          // Next/Complete button
          Expanded(
            child: ElevatedButton(
              onPressed: (_isCurrentStepValid() && !_isSaving) ? _nextPage : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _coffeeColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                disabledBackgroundColor: Colors.grey[300],
              ),
              child: _isSaving
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CoffeeWaveProgressIndicator(
                            size: 20,
                            coffeeColor: Colors.white,
                            backgroundColor: Colors.white24,
                            progressColor: Colors.white,
                            textColor: Colors.white,
                            showPercentage: false,
                            duration: Duration(seconds: 2),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('Kaydediliyor...'),
                      ],
                    )
                  : Text(_currentPage == 1 ? 'Tamamla' : 'İleri'),
            ),
          ),
        ],
      ),
    );
  }
}
