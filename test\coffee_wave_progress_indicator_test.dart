import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:siptracker/widgets/coffee_wave_progress_indicator.dart';

void main() {
  group('CoffeeWaveProgressIndicator Tests', () {
    testWidgets('should create widget with default parameters', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(size: 100.0),
          ),
        ),
      );

      // Assert
      expect(find.byType(CoffeeWaveProgressIndicator), findsOneWidget);
      
      // Check if the widget has the correct size
      final widget = tester.widget<CoffeeWaveProgressIndicator>(
        find.byType(CoffeeWaveProgressIndicator),
      );
      expect(widget.size, 100.0);
      expect(widget.showPercentage, true); // Default value
    });

    testWidgets('should create widget with custom parameters', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(
              size: 80.0,
              coffeeColor: Colors.brown,
              backgroundColor: Colors.grey,
              progressColor: Colors.black,
              textColor: Colors.white,
              showPercentage: false,
              duration: Duration(seconds: 5),
            ),
          ),
        ),
      );

      // Assert
      final widget = tester.widget<CoffeeWaveProgressIndicator>(
        find.byType(CoffeeWaveProgressIndicator),
      );
      
      expect(widget.size, 80.0);
      expect(widget.coffeeColor, Colors.brown);
      expect(widget.backgroundColor, Colors.grey);
      expect(widget.progressColor, Colors.black);
      expect(widget.textColor, Colors.white);
      expect(widget.showPercentage, false);
      expect(widget.duration, const Duration(seconds: 5));
    });

    testWidgets('should have proper semantic labels for accessibility', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(size: 100.0),
          ),
        ),
      );

      // Assert
      expect(find.bySemanticsLabel('Loading progress indicator'), findsOneWidget);
    });

    testWidgets('should render CustomPaint widgets for animation', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(size: 100.0),
          ),
        ),
      );

      // Assert - Should have at least two CustomPaint widgets (wave and progress layers)
      // Note: MaterialApp and Scaffold may add additional CustomPaint widgets
      expect(find.byType(CustomPaint), findsAtLeastNWidgets(2));
    });

    testWidgets('should be wrapped in RepaintBoundary for performance', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(size: 100.0),
          ),
        ),
      );

      // Assert - Should have at least one RepaintBoundary (our widget's boundary)
      // Note: MaterialApp and Scaffold may add additional RepaintBoundary widgets
      expect(find.byType(RepaintBoundary), findsAtLeastNWidgets(1));
    });

    testWidgets('should have correct size constraints', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(size: 120.0),
          ),
        ),
      );

      // Assert
      final sizedBox = tester.widget<SizedBox>(
        find.ancestor(
          of: find.byType(RepaintBoundary),
          matching: find.byType(SizedBox),
        ),
      );
      
      expect(sizedBox.width, 120.0);
      expect(sizedBox.height, 120.0);
    });

    testWidgets('should start animations automatically', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(
              size: 100.0,
              duration: Duration(milliseconds: 100),
            ),
          ),
        ),
      );

      // Let some animation frames pass
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 50));

      // Assert - Widget should still be present and animating
      expect(find.byType(CoffeeWaveProgressIndicator), findsOneWidget);
    });

    testWidgets('should handle different sizes correctly', (WidgetTester tester) async {
      // Test small size
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(size: 20.0),
          ),
        ),
      );
      
      expect(find.byType(CoffeeWaveProgressIndicator), findsOneWidget);

      // Test large size
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(size: 200.0),
          ),
        ),
      );
      
      expect(find.byType(CoffeeWaveProgressIndicator), findsOneWidget);
    });

    testWidgets('should work with showPercentage disabled', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(
              size: 100.0,
              showPercentage: false,
            ),
          ),
        ),
      );

      // Assert
      final widget = tester.widget<CoffeeWaveProgressIndicator>(
        find.byType(CoffeeWaveProgressIndicator),
      );
      expect(widget.showPercentage, false);
    });

    testWidgets('should handle coffee theme colors correctly', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CoffeeWaveProgressIndicator(
              size: 100.0,
              coffeeColor: Color(0xFF6F4E37), // Coffee brown
              backgroundColor: Color(0xFFE0E0E0), // Light cream
              progressColor: Color(0xFF4A4A4A), // Dark gray
              textColor: Color(0xFF2C2C2C), // Dark text
            ),
          ),
        ),
      );

      // Assert
      final widget = tester.widget<CoffeeWaveProgressIndicator>(
        find.byType(CoffeeWaveProgressIndicator),
      );
      
      expect(widget.coffeeColor, const Color(0xFF6F4E37));
      expect(widget.backgroundColor, const Color(0xFFE0E0E0));
      expect(widget.progressColor, const Color(0xFF4A4A4A));
      expect(widget.textColor, const Color(0xFF2C2C2C));
    });
  });
}
