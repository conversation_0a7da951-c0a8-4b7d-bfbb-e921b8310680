import 'dart:math';
import 'package:flutter/material.dart';

/// A custom coffee-themed wave progress indicator widget
/// 
/// This widget displays progress through both a circular fill animation and an
/// animated coffee wave inside the circle, providing a cohesive coffee-themed
/// user experience for the SipTracker app.
class CoffeeWaveProgressIndicator extends StatefulWidget {
  /// Circle diameter in logical pixels
  final double size;
  
  /// Wave color (coffee brown)
  final Color coffeeColor;
  
  /// Circle background color (light cream)
  final Color backgroundColor;
  
  /// Progress arc color (darker shade of coffee)
  final Color progressColor;
  
  /// Progress text color (contrasting color for readability)
  final Color textColor;
  
  /// Animation duration for progress fill
  final Duration duration;
  
  /// Whether to show percentage text in center
  final bool showPercentage;

  /// Creates a coffee wave progress indicator
  /// 
  /// [size] is required and determines the circle diameter.
  /// All other parameters have coffee-themed defaults.
  const CoffeeWaveProgressIndicator({
    super.key,
    required this.size,
    this.coffeeColor = const Color(0xFF6F4E37), // Coffee brown
    this.backgroundColor = const Color(0xFFE0E0E0), // Light cream
    this.progressColor = const Color(0xFF4A4A4A), // Dark gray
    this.textColor = const Color(0xFF2C2C2C), // Dark text
    this.duration = const Duration(seconds: 3),
    this.showPercentage = true,
  });

  @override
  State<CoffeeWaveProgressIndicator> createState() => _CoffeeWaveProgressIndicatorState();
}

class _CoffeeWaveProgressIndicatorState extends State<CoffeeWaveProgressIndicator>
    with TickerProviderStateMixin {
  
  /// Controls the fill level progress (0% to 100%)
  late AnimationController _progressController;
  
  /// Controls the wave oscillation (continuous cycles)
  late AnimationController _waveController;
  
  /// Progress animation with smooth easing
  late Animation<double> _progressAnimation;
  
  /// Wave animation for horizontal movement
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize progress controller
    _progressController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    // Initialize wave controller with continuous 2-second cycles
    _waveController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    // Create progress animation with smooth easing
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
    
    // Create wave animation for horizontal movement
    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * pi,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.linear,
    ));
    
    // Start animations
    _progressController.forward();
    _waveController.repeat();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final circleRadius = widget.size / 2;
    
    return Semantics(
      label: 'Loading progress indicator',
      value: 'Coffee wave animation in progress',
      child: SizedBox(
        width: widget.size,
        height: widget.size,
        child: RepaintBoundary(
          child: Stack(
            children: [
              // Background wave layer
              CustomPaint(
                size: Size(widget.size, widget.size),
                painter: CoffeeWavePainter(
                  progressAnimation: _progressAnimation,
                  waveAnimation: _waveAnimation,
                  circleRadius: circleRadius,
                  coffeeColor: widget.coffeeColor,
                ),
              ),
              // Foreground progress arc and text layer
              CustomPaint(
                size: Size(widget.size, widget.size),
                painter: CoffeeProgressPainter(
                  progressAnimation: _progressAnimation,
                  circleRadius: circleRadius,
                  backgroundColor: widget.backgroundColor,
                  progressColor: widget.progressColor,
                  textColor: widget.textColor,
                  showPercentage: widget.showPercentage,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Custom painter for rendering the coffee wave animation
class CoffeeWavePainter extends CustomPainter {
  final Animation<double> progressAnimation;
  final Animation<double> waveAnimation;
  final double circleRadius;
  final Color coffeeColor;

  CoffeeWavePainter({
    required this.progressAnimation,
    required this.waveAnimation,
    required this.circleRadius,
    required this.coffeeColor,
  }) : super(repaint: Listenable.merge([progressAnimation, waveAnimation]));

  @override
  void paint(Canvas canvas, Size size) {
    // Translate canvas to center
    canvas.translate(size.width / 2, size.height / 2);
    
    // Clip to circle
    final clipPath = Path()..addOval(Rect.fromCircle(center: Offset.zero, radius: circleRadius));
    canvas.clipPath(clipPath);
    
    // Wave calculations
    final amplitude = circleRadius * 0.08; // Wave height
    final frequency = 2.0; // Wave frequency
    final offsetX = circleRadius * 2 * waveAnimation.value; // Horizontal movement
    final fillLevel = circleRadius - (2 * circleRadius * progressAnimation.value); // Vertical fill position
    
    // Create wave path
    final wavePath = Path();
    bool firstPoint = true;
    
    // Generate wave points
    for (double x = -circleRadius; x <= circleRadius; x += 2.0) {
      final y = amplitude * sin(frequency * (x + offsetX)) + fillLevel;
      
      if (firstPoint) {
        wavePath.moveTo(x, y);
        firstPoint = false;
      } else {
        wavePath.lineTo(x, y);
      }
    }
    
    // Close the path to create a filled area
    wavePath.lineTo(circleRadius, circleRadius);
    wavePath.lineTo(-circleRadius, circleRadius);
    wavePath.close();
    
    // Paint the wave
    final wavePaint = Paint()
      ..color = coffeeColor
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(wavePath, wavePaint);
    
    // Add a second wave with slightly different properties for more realistic effect
    final secondWavePath = Path();
    firstPoint = true;
    final secondAmplitude = amplitude * 0.7;
    final secondOffsetX = offsetX * 1.3;
    
    for (double x = -circleRadius; x <= circleRadius; x += 2.0) {
      final y = secondAmplitude * sin(frequency * 1.5 * (x + secondOffsetX)) + fillLevel - amplitude * 0.3;
      
      if (firstPoint) {
        secondWavePath.moveTo(x, y);
        firstPoint = false;
      } else {
        secondWavePath.lineTo(x, y);
      }
    }
    
    secondWavePath.lineTo(circleRadius, circleRadius);
    secondWavePath.lineTo(-circleRadius, circleRadius);
    secondWavePath.close();
    
    // Paint the second wave with slightly different opacity
    final secondWavePaint = Paint()
      ..color = coffeeColor.withValues(alpha: 0.6)
      ..style = PaintingStyle.fill;
    
    canvas.drawPath(secondWavePath, secondWavePaint);
  }

  @override
  bool shouldRepaint(CoffeeWavePainter oldDelegate) {
    return oldDelegate.progressAnimation != progressAnimation ||
           oldDelegate.waveAnimation != waveAnimation ||
           oldDelegate.circleRadius != circleRadius ||
           oldDelegate.coffeeColor != coffeeColor;
  }
}

/// Custom painter for rendering the progress arc and percentage text
class CoffeeProgressPainter extends CustomPainter {
  final Animation<double> progressAnimation;
  final double circleRadius;
  final Color backgroundColor;
  final Color progressColor;
  final Color textColor;
  final bool showPercentage;

  CoffeeProgressPainter({
    required this.progressAnimation,
    required this.circleRadius,
    required this.backgroundColor,
    required this.progressColor,
    required this.textColor,
    required this.showPercentage,
  }) : super(repaint: progressAnimation);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final strokeWidth = circleRadius * 0.08;

    // Draw background circle (stroke only)
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, circleRadius - strokeWidth / 2, backgroundPaint);

    // Draw progress arc
    final progressPaint = Paint()
      ..color = progressColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final rect = Rect.fromCircle(center: center, radius: circleRadius - strokeWidth / 2);
    final startAngle = -pi / 2; // Start from top
    final sweepAngle = 2 * pi * progressAnimation.value;

    canvas.drawArc(rect, startAngle, sweepAngle, false, progressPaint);

    // Draw percentage text if enabled
    if (showPercentage) {
      final percentage = (progressAnimation.value * 100).toInt();
      final textSpan = TextSpan(
        text: '$percentage%',
        style: TextStyle(
          color: textColor,
          fontSize: circleRadius * 0.25,
          fontWeight: FontWeight.bold,
        ),
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );

      textPainter.layout();

      final textOffset = Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      );

      textPainter.paint(canvas, textOffset);
    }
  }

  @override
  bool shouldRepaint(CoffeeProgressPainter oldDelegate) {
    return oldDelegate.progressAnimation != progressAnimation ||
           oldDelegate.circleRadius != circleRadius ||
           oldDelegate.backgroundColor != backgroundColor ||
           oldDelegate.progressColor != progressColor ||
           oldDelegate.textColor != textColor ||
           oldDelegate.showPercentage != showPercentage;
  }
}
