import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../screens/auth_screen.dart';
import '../screens/onboarding_screen.dart';
import '../services/supabase_service.dart';
import 'main_navigation.dart';
import 'coffee_wave_progress_indicator.dart';

/// Authentication gate widget that controls app routing based on authentication state
///
/// This widget acts as a routing controller that automatically displays the appropriate
/// screen based on the user's authentication status:
/// - Shows AuthScreen when user is not authenticated
/// - Shows OnboardingScreen when user is authenticated but hasn't completed profile
/// - Shows MainNavigation when user is authenticated and has completed profile
///
/// The widget listens to global authentication state changes and updates the UI
/// accordingly without requiring manual navigation calls.
class AuthGate extends StatefulWidget {
  const AuthGate({super.key});

  @override
  State<AuthGate> createState() => _AuthGateState();
}

class _AuthGateState extends State<AuthGate> {
  final SupabaseService _supabaseService = SupabaseService();

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<User?>(
      valueListenable: currentUser,
      builder: (context, user, child) {
        // Show loading indicator while determining authentication state
        if (user == null && _isCheckingAuth) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CoffeeWaveProgressIndicator(size: 80.0),
                  SizedBox(height: 16),
                  Text('Loading...'),
                ],
              ),
            ),
          );
        }

        // Show appropriate screen based on authentication state
        if (user != null) {
          // User is authenticated - check if onboarding is completed
          return FutureBuilder<bool>(
            future: _supabaseService.hasCompletedOnboarding(),
            builder: (context, snapshot) {
              // Show loading while checking profile completion
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Scaffold(
                  body: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CoffeeWaveProgressIndicator(size: 80.0),
                        SizedBox(height: 16),
                        Text('Profil kontrol ediliyor...'),
                      ],
                    ),
                  ),
                );
              }

              // Check if user has completed onboarding
              final hasCompletedOnboarding = snapshot.data ?? false;

              if (hasCompletedOnboarding) {
                // User has completed onboarding - show main app
                return const MainNavigation();
              } else {
                // User needs to complete onboarding
                return const OnboardingScreen();
              }
            },
          );
        } else {
          // User is not authenticated - show authentication screen
          return const AuthScreen();
        }
      },
    );
  }
}

/// Global authentication state notifier
/// 
/// This ValueNotifier holds the current authenticated user and is updated
/// automatically by the authentication state listener in main.dart.
final ValueNotifier<User?> currentUser = ValueNotifier<User?>(null);

/// Flag to track if we're still checking initial authentication state
bool _isCheckingAuth = true;

/// Updates the global authentication state
/// 
/// This function is called by the authentication state listener to update
/// the global user state when authentication events occur.
void updateAuthState(User? user) {
  currentUser.value = user;
  _isCheckingAuth = false;
}
