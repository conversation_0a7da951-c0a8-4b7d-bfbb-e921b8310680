# SipTracker

A location-based Flutter application with Material 3 design.

## Project Structure

```
lib/
├── main.dart                 # App entry point with Material 3 theme
├── screens/                  # Main screen widgets
│   ├── home_screen.dart      # Home/Discover screen
│   ├── map_screen.dart       # Map screen for displaying locations
│   ├── favorites_screen.dart # Favorites screen
│   ├── profile_screen.dart   # User profile screen
│   └── auth_screen.dart      # User authentication (login/registration)
├── widgets/                  # Reusable UI components
│   ├── main_navigation.dart  # Bottom navigation implementation
│   └── auth_gate.dart        # Authentication-aware routing controller
├── models/                   # Data models (to be added)
├── services/                 # External service integrations
│   ├── supabase_service.dart # Supabase client service layer
│   └── location_service.dart # Location-specific database operations
└── utils/                    # Helper functions and constants
```

## Features

- ✅ Material 3 design system
- ✅ Bottom navigation with 4 tabs (Home, Map, Favorites, Profile)
- ✅ Proper state management for navigation
- ✅ Clean project structure following Flutter best practices
- ✅ Null safety enabled
- ✅ Basic error handling
- ✅ Supabase integration with service layer architecture
- ✅ Connection status indicator
- ✅ Ready for authentication and database operations
- ✅ Complete authentication UI with login/registration forms
- ✅ Form validation and user experience optimizations
- ✅ Full Supabase authentication integration (sign-up/sign-in)
- ✅ Comprehensive error handling and user feedback
- ✅ Global authentication state management with automatic routing
- ✅ Sign-out functionality with seamless navigation

## Navigation

The app uses a bottom navigation bar with the following screens:
- **Home** (🏠): Discover screen for finding locations
- **Map** (🗺️): Interactive map view
- **Favorites** (❤️): Saved favorite locations
- **Profile** (👤): User account and preferences

## Getting Started

1. Ensure you have Flutter installed
2. Run `flutter pub get` to install dependencies
3. Run `flutter run` to start the application
4. Run `flutter test` to execute tests

## Supabase Integration

The app includes a complete Supabase integration with:

- **Service Layer Architecture**: Clean separation of concerns with dedicated service classes
- **Error Handling**: Comprehensive error handling for network and database operations
- **Connection Status**: Visual indicator showing Supabase connection status
- **Ready for Production**: Placeholder credentials with TODO comments for secure configuration

### Setting up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Get your project URL and anon key from the project settings
3. Replace the placeholder values in `lib/main.dart`:
   ```dart
   await Supabase.initialize(
     url: 'YOUR_ACTUAL_SUPABASE_URL',
     anonKey: 'YOUR_ACTUAL_SUPABASE_ANON_KEY',
   );
   ```

### Using Supabase Services

```dart
// Get Supabase service instance
final supabaseService = SupabaseService();

// Check connection status
if (supabaseService.isInitialized) {
  // Perform database operations
  final locations = await LocationService().getAllLocations();
}

// Authentication
final user = supabaseService.currentUser;
final isLoggedIn = supabaseService.isAuthenticated;
```

### Authentication Screen

The app includes a complete authentication UI (`AuthScreen`) with full Supabase integration:

- **Toggle Interface**: Switch between login and registration modes
- **Form Validation**: Email format, password strength, and confirmation matching
- **Material 3 Design**: Consistent with app theme and responsive layout
- **User Experience**: Loading states, error handling, and visual feedback
- **Supabase Integration**: Complete sign-up and sign-in functionality
- **Error Handling**: User-friendly error messages for common authentication issues
- **Graceful Degradation**: Works even when Supabase is not properly configured
- **Automatic Navigation**: Seamless routing based on authentication state
- **Global State Management**: Real-time authentication state updates across the app

```dart
// Navigate to authentication screen
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const AuthScreen()),
);
```

#### Authentication Features

- **Sign Up**: Creates new user accounts with email verification
- **Sign In**: Authenticates existing users with email and password
- **Error Handling**: Comprehensive error messages for various scenarios:
  - Invalid credentials
  - User already exists
  - Email not verified
  - Network connectivity issues
  - Weak passwords
- **Loading States**: Visual feedback during authentication operations
- **Form Validation**: Client-side validation before API calls
- **Sign-Out**: Secure sign-out functionality with automatic navigation to login

#### Authentication Flow

The app implements a comprehensive authentication system with automatic state management:

1. **Initial Load**: App checks authentication state and shows appropriate screen
2. **Sign Up**: Creates account, shows verification message, switches to login mode
3. **Sign In**: Authenticates user and automatically navigates to main app
4. **Sign Out**: Securely signs out user and returns to authentication screen
5. **State Persistence**: Authentication state is maintained across app sessions

## Development

- The app uses Material 3 design system (`useMaterial3: true`)
- Navigation is handled by `MainNavigation` widget using `IndexedStack`
- Each screen is a separate widget in the `screens/` directory
- Supabase integration follows service layer pattern for clean architecture
- Follow Flutter naming conventions (snake_case for files)
