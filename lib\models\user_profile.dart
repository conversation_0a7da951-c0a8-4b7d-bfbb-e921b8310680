/// User profile data model for the SipTracker coffee shop app
/// 
/// This model represents a complete user profile with coffee preferences
/// fetched from the Supabase 'profiles' table.
class UserProfile {
  final String? fullName;
  final String? avatarUrl;
  final CoffeePreferences? coffeePreferences;
  final bool onboardingCompleted;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserProfile({
    this.fullName,
    this.avatarUrl,
    this.coffeePreferences,
    this.onboardingCompleted = false,
    this.createdAt,
    this.updatedAt,
  });

  /// Creates a UserProfile from JSON data
  /// 
  /// [json] Map containing profile data from Supabase
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      fullName: json['full_name'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      coffeePreferences: json['coffee_preferences'] != null
          ? CoffeePreferences.fromJson(json['coffee_preferences'] as Map<String, dynamic>)
          : null,
      onboardingCompleted: json['onboarding_completed'] as bool? ?? false,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'] as String)
          : null,
    );
  }

  /// Converts UserProfile to JSON
  Map<String, dynamic> toJson() {
    return {
      'full_name': fullName,
      'avatar_url': avatarUrl,
      'coffee_preferences': coffeePreferences?.toJson(),
      'onboarding_completed': onboardingCompleted,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Gets user initials from full name for avatar fallback
  String get initials {
    if (fullName == null || fullName!.trim().isEmpty) {
      return '?';
    }
    
    final nameParts = fullName!.trim().split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts.first[0]}${nameParts.last[0]}'.toUpperCase();
    } else if (nameParts.isNotEmpty) {
      return nameParts.first[0].toUpperCase();
    }
    
    return '?';
  }

  /// Checks if profile has basic information
  bool get hasBasicInfo {
    return fullName != null && fullName!.trim().isNotEmpty;
  }

  /// Checks if profile has coffee preferences
  bool get hasCoffeePreferences {
    return coffeePreferences != null && coffeePreferences!.isValid;
  }
}

/// Coffee preferences data model
/// 
/// This model represents structured coffee preference data stored as JSONB
/// in the Supabase 'profiles' table.
class CoffeePreferences {
  final List<String> favoriteTypes;
  final String? roastLevel;
  final List<String> milkOptions;
  final bool sweetened;

  const CoffeePreferences({
    this.favoriteTypes = const [],
    this.roastLevel,
    this.milkOptions = const [],
    this.sweetened = false,
  });

  /// Creates CoffeePreferences from JSON data
  /// 
  /// [json] Map containing coffee preferences data
  factory CoffeePreferences.fromJson(Map<String, dynamic> json) {
    return CoffeePreferences(
      favoriteTypes: (json['favorite_types'] as List<dynamic>?)
          ?.map((e) => e.toString())
          .toList() ?? [],
      roastLevel: json['roast_level'] as String?,
      milkOptions: (json['milk_options'] as List<dynamic>?)
          ?.map((e) => e.toString())
          .toList() ?? [],
      sweetened: json['sweetened'] as bool? ?? false,
    );
  }

  /// Converts CoffeePreferences to JSON
  Map<String, dynamic> toJson() {
    return {
      'favorite_types': favoriteTypes,
      'roast_level': roastLevel,
      'milk_options': milkOptions,
      'sweetened': sweetened,
    };
  }

  /// Checks if coffee preferences have any data
  bool get isValid {
    return favoriteTypes.isNotEmpty || 
           roastLevel != null || 
           milkOptions.isNotEmpty;
  }

  /// Gets a summary of coffee preferences for display
  String get summary {
    final parts = <String>[];
    
    if (favoriteTypes.isNotEmpty) {
      parts.add('${favoriteTypes.length} kahve türü');
    }
    
    if (roastLevel != null) {
      parts.add(roastLevel!);
    }
    
    if (milkOptions.isNotEmpty) {
      parts.add('${milkOptions.length} süt seçeneği');
    }
    
    if (sweetened) {
      parts.add('şekerli');
    } else {
      parts.add('şekersiz');
    }
    
    return parts.join(', ');
  }
}
