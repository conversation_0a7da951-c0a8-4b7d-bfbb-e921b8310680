// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:siptracker/main.dart';
import 'package:siptracker/screens/auth_screen.dart';

void main() {
  testWidgets('SipTracker app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const SipTrackerApp());

    // Wait for initial frame
    await tester.pump();

    // Verify that the app loads (either AuthScreen or loading state)
    expect(find.byType(MaterialApp), findsOneWidget);

    // The app should show either loading or auth screen initially
    expect(
      find.byType(AuthScreen).evaluate().isNotEmpty ||
      find.text('Loading...').evaluate().isNotEmpty,
      isTrue,
    );
  });

  testWidgets('AuthScreen widget test', (WidgetTester tester) async {
    // Build the AuthScreen widget
    await tester.pumpWidget(
      MaterialApp(
        theme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        ),
        home: const AuthScreen(),
      ),
    );

    // Verify that the auth screen loads correctly
    expect(find.text('Welcome Back'), findsOneWidget);
    expect(find.text('SipTracker'), findsOneWidget);
    expect(find.text('Sign In'), findsOneWidget);

    // Verify form fields are present
    expect(find.byType(TextFormField), findsNWidgets(2)); // Email and password

    // Test toggle to registration mode by tapping the TextButton
    await tester.tap(find.byType(TextButton));
    await tester.pump();

    // Verify registration mode
    expect(find.text('Create Account'), findsOneWidget);
    expect(find.byType(TextFormField), findsNWidgets(3)); // Email, password, confirm password
  });
}
