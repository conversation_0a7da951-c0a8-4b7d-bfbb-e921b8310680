import 'package:flutter_test/flutter_test.dart';
import 'package:siptracker/models/review.dart';

void main() {
  group('Review Model Tests', () {
    test('should create Review from JSON correctly', () {
      // Arrange
      final json = {
        'id': 1,
        'shop_id': 123,
        'user_id': 'user-uuid-123',
        'rating': 5,
        'comment': 'Great coffee and atmosphere!',
        'created_at': '2023-01-01T12:00:00Z',
        'user_email': '<EMAIL>',
      };

      // Act
      final review = Review.fromJson(json);

      // Assert
      expect(review.id, 1);
      expect(review.shopId, 123);
      expect(review.userId, 'user-uuid-123');
      expect(review.rating, 5);
      expect(review.comment, 'Great coffee and atmosphere!');
      expect(review.createdAt, DateTime.parse('2023-01-01T12:00:00Z'));
      expect(review.userEmail, '<EMAIL>');
    });

    test('should handle null user email correctly', () {
      // Arrange
      final json = {
        'id': 2,
        'shop_id': 456,
        'user_id': 'user-uuid-456',
        'rating': 4,
        'comment': 'Good coffee',
        'created_at': '2023-01-02T12:00:00Z',
        'user_email': null,
      };

      // Act
      final review = Review.fromJson(json);

      // Assert
      expect(review.userEmail, null);
      expect(review.displayName, 'Anonymous');
    });

    test('should convert to JSON correctly', () {
      // Arrange
      final review = Review(
        id: 3,
        shopId: 789,
        userId: 'user-uuid-789',
        rating: 3,
        comment: 'Average experience',
        createdAt: DateTime.parse('2023-01-03T12:00:00Z'),
        userEmail: '<EMAIL>',
      );

      // Act
      final json = review.toJson();

      // Assert
      expect(json['id'], 3);
      expect(json['shop_id'], 789);
      expect(json['user_id'], 'user-uuid-789');
      expect(json['rating'], 3);
      expect(json['comment'], 'Average experience');
      expect(json['created_at'], '2023-01-03T12:00:00.000Z');
      expect(json['user_email'], '<EMAIL>');
    });

    test('should handle copyWith correctly', () {
      // Arrange
      final original = Review(
        id: 4,
        shopId: 101,
        userId: 'user-uuid-101',
        rating: 2,
        comment: 'Not great',
        createdAt: DateTime.parse('2023-01-04T12:00:00Z'),
        userEmail: '<EMAIL>',
      );

      // Act
      final updated = original.copyWith(
        rating: 4,
        comment: 'Actually, it was better than I thought',
      );

      // Assert
      expect(updated.id, original.id);
      expect(updated.shopId, original.shopId);
      expect(updated.userId, original.userId);
      expect(updated.rating, 4);
      expect(updated.comment, 'Actually, it was better than I thought');
      expect(updated.createdAt, original.createdAt);
      expect(updated.userEmail, original.userEmail);
      expect(original.rating, 2); // Original should be unchanged
    });

    test('should handle equality correctly', () {
      // Arrange
      final review1 = Review(
        id: 5,
        shopId: 202,
        userId: 'user-uuid-202',
        rating: 5,
        comment: 'Excellent!',
        createdAt: DateTime.parse('2023-01-05T12:00:00Z'),
      );

      final review2 = Review(
        id: 5,
        shopId: 303,
        userId: 'user-uuid-303',
        rating: 1,
        comment: 'Terrible!',
        createdAt: DateTime.parse('2023-01-06T12:00:00Z'),
      );

      final review3 = Review(
        id: 6,
        shopId: 202,
        userId: 'user-uuid-202',
        rating: 5,
        comment: 'Excellent!',
        createdAt: DateTime.parse('2023-01-05T12:00:00Z'),
      );

      // Act & Assert
      expect(review1, review2); // Same ID
      expect(review1, isNot(review3)); // Different ID
      expect(review1.hashCode, review2.hashCode); // Same hash
    });

    test('should validate review data correctly', () {
      // Arrange & Act & Assert
      final validReview = Review(
        id: 7,
        shopId: 404,
        userId: 'user-uuid-404',
        rating: 3,
        comment: 'Decent coffee',
        createdAt: DateTime.now(),
      );
      expect(validReview.isValid, true);

      final invalidRatingLow = validReview.copyWith(rating: 0);
      expect(invalidRatingLow.isValid, false);

      final invalidRatingHigh = validReview.copyWith(rating: 6);
      expect(invalidRatingHigh.isValid, false);

      final invalidComment = validReview.copyWith(comment: '   ');
      expect(invalidComment.isValid, false);
    });

    test('should create review for submission correctly', () {
      // Arrange & Act
      final review = Review.forSubmission(
        shopId: 505,
        userId: 'user-uuid-505',
        rating: 4,
        comment: '  Great place!  ',
        userEmail: '<EMAIL>',
      );

      // Assert
      expect(review.id, 0); // Should be 0 for new submissions
      expect(review.shopId, 505);
      expect(review.userId, 'user-uuid-505');
      expect(review.rating, 4);
      expect(review.comment, 'Great place!'); // Should be trimmed
      expect(review.userEmail, '<EMAIL>');
      expect(review.isValid, true);
    });

    test('should convert to submission JSON correctly', () {
      // Arrange
      final review = Review.forSubmission(
        shopId: 606,
        userId: 'user-uuid-606',
        rating: 5,
        comment: 'Amazing coffee!',
        userEmail: '<EMAIL>',
      );

      // Act
      final json = review.toSubmissionJson();

      // Assert
      expect(json.containsKey('id'), false); // Should not include ID
      expect(json.containsKey('created_at'), false); // Should not include created_at
      expect(json['shop_id'], 606);
      expect(json['user_id'], 'user-uuid-606');
      expect(json['rating'], 5);
      expect(json['comment'], 'Amazing coffee!');
      expect(json['user_email'], '<EMAIL>');
    });

    test('should display correct user name', () {
      // Arrange & Act & Assert
      final reviewWithEmail = Review(
        id: 8,
        shopId: 707,
        userId: 'user-uuid-707',
        rating: 4,
        comment: 'Good coffee',
        createdAt: DateTime.now(),
        userEmail: '<EMAIL>',
      );
      expect(reviewWithEmail.displayName, '<EMAIL>');

      final reviewWithoutEmail = Review(
        id: 9,
        shopId: 808,
        userId: 'user-uuid-808',
        rating: 3,
        comment: 'Okay coffee',
        createdAt: DateTime.now(),
        userEmail: null,
      );
      expect(reviewWithoutEmail.displayName, 'Anonymous');

      final reviewWithEmptyEmail = Review(
        id: 10,
        shopId: 909,
        userId: 'user-uuid-909',
        rating: 2,
        comment: 'Not great',
        createdAt: DateTime.now(),
        userEmail: '',
      );
      expect(reviewWithEmptyEmail.displayName, 'Anonymous');
    });
  });
}
