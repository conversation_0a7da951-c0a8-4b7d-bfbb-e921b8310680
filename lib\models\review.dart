/// Data model representing a coffee shop review
/// 
/// This model handles data from the Supabase 'yorumlar' table and provides
/// proper type safety and JSON serialization for review information.
class Review {
  /// Unique identifier for the review (integer ID from Supabase)
  final int id;
  
  /// Foreign key referencing the coffee shop (mekanlar.id)
  final int shopId;
  
  /// UUID of the user who wrote the review
  final String userId;
  
  /// Rating from 1-5 stars
  final int rating;
  
  /// Review text content
  final String comment;
  
  /// Timestamp when the review was created
  final DateTime createdAt;
  
  /// Optional user email for display purposes
  final String? userEmail;

  /// Creates a new Review instance
  const Review({
    required this.id,
    required this.shopId,
    required this.userId,
    required this.rating,
    required this.comment,
    required this.createdAt,
    this.userEmail,
  });

  /// Creates a Review instance from JSON data (typically from Supabase)
  /// 
  /// Handles the conversion of Supabase's snake_case field names to Dart
  /// properties and properly parses the created_at timestamp.
  /// 
  /// Example usage:
  /// ```dart
  /// final review = Review.fromJson(supabaseResponse);
  /// ```
  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'] as int,
      shopId: json['shop_id'] as int,
      userId: json['user_id'] as String,
      rating: json['rating'] as int,
      comment: json['comment'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      userEmail: json['user_email'] as String?,
    );
  }

  /// Converts the Review instance to JSON format
  /// 
  /// Useful for sending data to Supabase or for serialization.
  /// Converts DateTime back to ISO string format expected by Supabase.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'shop_id': shopId,
      'user_id': userId,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
      'user_email': userEmail,
    };
  }

  /// Creates a copy of this Review with optionally updated fields
  Review copyWith({
    int? id,
    int? shopId,
    String? userId,
    int? rating,
    String? comment,
    DateTime? createdAt,
    String? userEmail,
  }) {
    return Review(
      id: id ?? this.id,
      shopId: shopId ?? this.shopId,
      userId: userId ?? this.userId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      createdAt: createdAt ?? this.createdAt,
      userEmail: userEmail ?? this.userEmail,
    );
  }

  @override
  String toString() {
    return 'Review(id: $id, shopId: $shopId, userId: $userId, '
           'rating: $rating, comment: ${comment.length > 50 ? '${comment.substring(0, 50)}...' : comment})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Review && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Returns a user-friendly display name for the reviewer
  String get displayName {
    if (userEmail != null && userEmail!.isNotEmpty) {
      return userEmail!;
    }
    return 'Anonymous';
  }

  /// Validates if the review data is valid for submission
  bool get isValid {
    return rating >= 1 && 
           rating <= 5 && 
           comment.trim().isNotEmpty;
  }

  /// Creates a Review instance for new submissions (without ID and createdAt)
  /// 
  /// Used when creating new reviews before sending to Supabase
  factory Review.forSubmission({
    required int shopId,
    required String userId,
    required int rating,
    required String comment,
    String? userEmail,
  }) {
    return Review(
      id: 0, // Will be set by Supabase
      shopId: shopId,
      userId: userId,
      rating: rating,
      comment: comment.trim(),
      createdAt: DateTime.now(), // Will be overridden by Supabase
      userEmail: userEmail,
    );
  }

  /// Converts to JSON for submission (excludes id and created_at)
  Map<String, dynamic> toSubmissionJson() {
    return {
      'shop_id': shopId,
      'user_id': userId,
      'rating': rating,
      'comment': comment.trim(),
      'user_email': userEmail,
    };
  }
}
