import 'supabase_service.dart';

/// Service class for managing location-related operations with Supabase
/// 
/// This service provides methods for CRUD operations on location data,
/// including creating, reading, updating, and deleting location records.
/// 
/// Example usage:
/// ```dart
/// final locationService = LocationService();
/// final locations = await locationService.getAllLocations();
/// ```
class LocationService {
  /// Private constructor to prevent external instantiation
  LocationService._();
  
  /// Singleton instance of LocationService
  static final LocationService _instance = LocationService._();
  
  /// Factory constructor that returns the singleton instance
  factory LocationService() => _instance;
  
  /// Get the Supabase service instance
  final SupabaseService _supabaseService = SupabaseService();
  
  /// Table name for locations in Supabase
  static const String _tableName = 'locations';
  
  /// Retrieves all locations from the database
  /// 
  /// Returns a [Future] that resolves to a list of location maps.
  /// Throws an exception if the operation fails.
  /// 
  /// Example:
  /// ```dart
  /// try {
  ///   final locations = await locationService.getAllLocations();
  ///   print('Found ${locations.length} locations');
  /// } catch (e) {
  ///   print('Error fetching locations: $e');
  /// }
  /// ```
  Future<List<Map<String, dynamic>>> getAllLocations() async {
    try {
      final response = await _supabaseService
          .table(_tableName)
          .select()
          .order('created_at', ascending: false);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch locations: $e');
    }
  }
  
  /// Retrieves a specific location by ID
  /// 
  /// [id] The unique identifier of the location.
  /// 
  /// Returns a [Future] that resolves to the location data or null if not found.
  /// Throws an exception if the operation fails.
  Future<Map<String, dynamic>?> getLocationById(String id) async {
    try {
      final response = await _supabaseService
          .table(_tableName)
          .select()
          .eq('id', id)
          .maybeSingle();
      
      return response;
    } catch (e) {
      throw Exception('Failed to fetch location with ID $id: $e');
    }
  }
  
  /// Creates a new location in the database
  /// 
  /// [locationData] A map containing the location data to be inserted.
  /// 
  /// Returns a [Future] that resolves to the created location data.
  /// Throws an exception if the operation fails.
  /// 
  /// Example:
  /// ```dart
  /// final newLocation = {
  ///   'name': 'Coffee Shop',
  ///   'latitude': 40.7128,
  ///   'longitude': -74.0060,
  ///   'description': 'Great coffee place',
  /// };
  /// 
  /// try {
  ///   final created = await locationService.createLocation(newLocation);
  ///   print('Created location with ID: ${created['id']}');
  /// } catch (e) {
  ///   print('Error creating location: $e');
  /// }
  /// ```
  Future<Map<String, dynamic>> createLocation(
    Map<String, dynamic> locationData,
  ) async {
    try {
      final response = await _supabaseService
          .table(_tableName)
          .insert(locationData)
          .select()
          .single();
      
      return response;
    } catch (e) {
      throw Exception('Failed to create location: $e');
    }
  }
  
  /// Updates an existing location in the database
  /// 
  /// [id] The unique identifier of the location to update.
  /// [updates] A map containing the fields to update.
  /// 
  /// Returns a [Future] that resolves to the updated location data.
  /// Throws an exception if the operation fails.
  Future<Map<String, dynamic>> updateLocation(
    String id,
    Map<String, dynamic> updates,
  ) async {
    try {
      final response = await _supabaseService
          .table(_tableName)
          .update(updates)
          .eq('id', id)
          .select()
          .single();
      
      return response;
    } catch (e) {
      throw Exception('Failed to update location with ID $id: $e');
    }
  }
  
  /// Deletes a location from the database
  /// 
  /// [id] The unique identifier of the location to delete.
  /// 
  /// Returns a [Future] that completes when the location is deleted.
  /// Throws an exception if the operation fails.
  Future<void> deleteLocation(String id) async {
    try {
      await _supabaseService
          .table(_tableName)
          .delete()
          .eq('id', id);
    } catch (e) {
      throw Exception('Failed to delete location with ID $id: $e');
    }
  }
  
  /// Searches for locations by name
  /// 
  /// [query] The search query string.
  /// 
  /// Returns a [Future] that resolves to a list of matching locations.
  /// Throws an exception if the operation fails.
  Future<List<Map<String, dynamic>>> searchLocationsByName(
    String query,
  ) async {
    try {
      final response = await _supabaseService
          .table(_tableName)
          .select()
          .ilike('name', '%$query%')
          .order('name');
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to search locations: $e');
    }
  }
  
  /// Gets locations within a specific radius of a point
  /// 
  /// [latitude] The latitude of the center point.
  /// [longitude] The longitude of the center point.
  /// [radiusKm] The radius in kilometers.
  /// 
  /// Returns a [Future] that resolves to a list of nearby locations.
  /// Note: This requires PostGIS extension and proper spatial indexing in Supabase.
  Future<List<Map<String, dynamic>>> getNearbyLocations(
    double latitude,
    double longitude,
    double radiusKm,
  ) async {
    try {
      // This is a simplified example. In a real implementation, you would use
      // PostGIS functions for accurate distance calculations.
      final response = await _supabaseService
          .table(_tableName)
          .select()
          .gte('latitude', latitude - (radiusKm / 111.0)) // Rough approximation
          .lte('latitude', latitude + (radiusKm / 111.0))
          .gte('longitude', longitude - (radiusKm / 111.0))
          .lte('longitude', longitude + (radiusKm / 111.0));
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to fetch nearby locations: $e');
    }
  }
}
