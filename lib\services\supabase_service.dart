import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:image_picker/image_picker.dart';
import '../models/coffee_shop.dart';
import '../models/review.dart';

/// Service class that provides access to Supabase client instance
/// 
/// This service acts as a centralized access point for all Supabase operations
/// including authentication, database queries, and real-time subscriptions.
/// 
/// Usage:
/// ```dart
/// final supabaseService = SupabaseService();
/// final client = supabaseService.client;
/// ```
class SupabaseService {
  /// Private constructor to prevent external instantiation
  SupabaseService._();
  
  /// Singleton instance of SupabaseService
  static final SupabaseService _instance = SupabaseService._();
  
  /// Factory constructor that returns the singleton instance
  factory SupabaseService() => _instance;
  
  /// Gets the Supabase client instance
  /// 
  /// Returns the initialized Supabase client that can be used for
  /// database operations, authentication, and real-time subscriptions.
  /// 
  /// Throws [Exception] if Supabase has not been initialized.
  SupabaseClient get client {
    try {
      return Supabase.instance.client;
    } catch (e) {
      throw Exception(
        'Supabase client not initialized. Make sure to call Supabase.initialize() '
        'in main() before using the client.',
      );
    }
  }
  
  /// Checks if Supabase has been initialized
  /// 
  /// Returns `true` if Supabase has been properly initialized, `false` otherwise.
  bool get isInitialized {
    try {
      Supabase.instance.client;
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Gets the current user session
  /// 
  /// Returns the current authenticated user session, or `null` if no user is logged in.
  Session? get currentSession => client.auth.currentSession;
  
  /// Gets the current authenticated user
  /// 
  /// Returns the current authenticated user, or `null` if no user is logged in.
  User? get currentUser => client.auth.currentUser;
  
  /// Checks if a user is currently authenticated
  /// 
  /// Returns `true` if a user is logged in, `false` otherwise.
  bool get isAuthenticated => currentUser != null;
  
  /// Signs out the current user
  /// 
  /// Returns a [Future] that completes when the user has been signed out.
  /// Throws an exception if the sign out operation fails.
  Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }
  
  /// Gets a reference to a specific table
  ///
  /// [tableName] The name of the table to get a reference to.
  ///
  /// Returns a [SupabaseQueryBuilder] for the specified table.
  ///
  /// Example:
  /// ```dart
  /// final locations = supabaseService.table('locations');
  /// final data = await locations.select().execute();
  /// ```
  SupabaseQueryBuilder table(String tableName) {
    return client.from(tableName);
  }

  /// Checks if the current user has completed their onboarding profile
  ///
  /// Returns `true` if the user has a profile record in the 'profiles' table,
  /// `false` if they don't have a profile or if Supabase is not initialized.
  ///
  /// This method is used by the AuthGate to determine whether to show the
  /// onboarding screen or the main navigation after authentication.
  ///
  /// Example usage:
  /// ```dart
  /// final supabaseService = SupabaseService();
  /// final hasProfile = await supabaseService.hasCompletedOnboarding();
  /// if (!hasProfile) {
  ///   // Show onboarding screen
  /// }
  /// ```
  Future<bool> hasCompletedOnboarding() async {
    try {
      // Check if Supabase is initialized and user is authenticated
      if (!isInitialized || !isAuthenticated) {
        debugPrint('SupabaseService: Supabase not initialized or user not authenticated');
        return false;
      }

      final userId = currentUser?.id;
      if (userId == null) {
        debugPrint('SupabaseService: No user ID available');
        return false;
      }

      debugPrint('SupabaseService: Checking profile completion for user: $userId');

      // Check if user has a profile record in the 'profiles' table
      // Use user_id field instead of id to match UUID
      final response = await client
          .from('profiles')
          .select('id')
          .eq('user_id', userId)
          .maybeSingle();

      final hasProfile = response != null;
      debugPrint('SupabaseService: User has completed profile: $hasProfile');

      return hasProfile;
    } catch (e) {
      debugPrint('SupabaseService: Error checking profile completion: $e');
      // Return false on error to show onboarding screen as a safe fallback
      return false;
    }
  }
  
  /// Gets a reference to Supabase Storage
  /// 
  /// Returns a [SupabaseStorageClient] for file operations.
  SupabaseStorageClient get storage => client.storage;
  
  /// Gets a reference to Supabase Auth
  /// 
  /// Returns a [GoTrueClient] for authentication operations.
  GoTrueClient get auth => client.auth;
  
  /// Gets a reference to Supabase Realtime
  ///
  /// Returns a [RealtimeClient] for real-time subscriptions.
  RealtimeClient get realtime => client.realtime;

  /// Fetches all coffee shops from the 'mekanlar' table
  ///
  /// Returns a [Future] that resolves to a list of [CoffeeShop] objects.
  /// Returns an empty list if no coffee shops are found or if Supabase is not initialized.
  ///
  /// This method includes comprehensive error handling and graceful degradation:
  /// - If Supabase is not initialized, returns an empty list
  /// - If the database query fails, logs the error and returns an empty list
  /// - If JSON parsing fails for individual records, skips those records and continues
  ///
  /// Example usage:
  /// ```dart
  /// final supabaseService = SupabaseService();
  /// final coffeeShops = await supabaseService.getCoffeeShops();
  /// print('Found ${coffeeShops.length} coffee shops');
  /// ```
  Future<List<CoffeeShop>> getCoffeeShops() async {
    try {
      // Check if Supabase is initialized before making the request
      if (!isInitialized) {
        debugPrint('SupabaseService: Supabase not initialized, returning empty list');
        return [];
      }

      debugPrint('SupabaseService: Fetching coffee shops from mekanlar table...');

      // Fetch data from the 'mekanlar' table, ordered by creation date (newest first)
      final response = await client
          .from('mekanlar')
          .select()
          .order('created_at', ascending: false);

      debugPrint('SupabaseService: Received ${response.length} records from database');

      // Convert the response to a list of CoffeeShop objects
      final List<CoffeeShop> coffeeShops = [];

      for (final item in response) {
        try {
          // Attempt to parse each record into a CoffeeShop object
          final coffeeShop = CoffeeShop.fromJson(item);
          coffeeShops.add(coffeeShop);
        } catch (parseError) {
          // Log parsing errors but continue processing other records
          debugPrint('SupabaseService: Failed to parse coffee shop record: $parseError');
          debugPrint('SupabaseService: Problematic record: $item');
          // Skip this record and continue with the next one
          continue;
        }
      }

      debugPrint('SupabaseService: Successfully parsed ${coffeeShops.length} coffee shops');
      return coffeeShops;

    } catch (e) {
      // Log the error for debugging purposes
      debugPrint('SupabaseService: Error fetching coffee shops: $e');

      // Return empty list to allow the app to continue functioning
      // This provides graceful degradation when the database is unavailable
      return [];
    }
  }

  /// Fetches all reviews for a specific coffee shop from the 'yorumlar' table
  ///
  /// [shopId] The ID of the coffee shop to get reviews for
  ///
  /// Returns a [Future] that resolves to a list of [Review] objects.
  /// Returns an empty list if no reviews are found or if Supabase is not initialized.
  ///
  /// This method includes comprehensive error handling and graceful degradation:
  /// - If Supabase is not initialized, returns an empty list
  /// - If the database query fails, logs the error and returns an empty list
  /// - If JSON parsing fails for individual records, skips those records and continues
  ///
  /// Example usage:
  /// ```dart
  /// final supabaseService = SupabaseService();
  /// final reviews = await supabaseService.getReviewsForShop(123);
  /// print('Found ${reviews.length} reviews');
  /// ```
  Future<List<Review>> getReviewsForShop(int shopId) async {
    try {
      // Check if Supabase is initialized before making the request
      if (!isInitialized) {
        debugPrint('SupabaseService: Supabase not initialized, returning empty reviews list');
        return [];
      }

      debugPrint('SupabaseService: Fetching reviews for shop ID $shopId from yorumlar table...');

      // Fetch data from the 'yorumlar' table, filtered by shop_id and ordered by creation date (newest first)
      final response = await client
          .from('yorumlar')
          .select()
          .eq('shop_id', shopId)
          .order('created_at', ascending: false);

      debugPrint('SupabaseService: Received ${response.length} review records from database');

      // Convert the response to a list of Review objects
      final List<Review> reviews = [];

      for (final item in response) {
        try {
          // Attempt to parse each record into a Review object
          final review = Review.fromJson(item);
          reviews.add(review);
        } catch (parseError) {
          // Log parsing errors but continue processing other records
          debugPrint('SupabaseService: Failed to parse review record: $parseError');
          debugPrint('SupabaseService: Problematic record: $item');
          // Skip this record and continue with the next one
          continue;
        }
      }

      debugPrint('SupabaseService: Successfully parsed ${reviews.length} reviews');
      return reviews;

    } catch (e) {
      // Log the error for debugging purposes
      debugPrint('SupabaseService: Error fetching reviews for shop $shopId: $e');

      // Return empty list to allow the app to continue functioning
      // This provides graceful degradation when the database is unavailable
      return [];
    }
  }

  /// Adds a new review to the 'yorumlar' table
  ///
  /// [review] The review object to be added (should use Review.forSubmission)
  ///
  /// Returns a [Future] that resolves to the created [Review] object with the generated ID.
  /// Throws an exception if the operation fails or if Supabase is not initialized.
  ///
  /// This method includes comprehensive error handling and validation:
  /// - Validates that Supabase is initialized
  /// - Validates the review data before submission
  /// - Returns the complete review object with server-generated ID and timestamp
  ///
  /// Example usage:
  /// ```dart
  /// final newReview = Review.forSubmission(
  ///   shopId: 123,
  ///   userId: 'user-uuid',
  ///   rating: 5,
  ///   comment: 'Great coffee!',
  ///   userEmail: '<EMAIL>',
  /// );
  ///
  /// try {
  ///   final createdReview = await supabaseService.addReview(newReview);
  ///   print('Review created with ID: ${createdReview.id}');
  /// } catch (e) {
  ///   print('Error creating review: $e');
  /// }
  /// ```
  Future<Review> addReview(Review review) async {
    try {
      // Check if Supabase is initialized before making the request
      if (!isInitialized) {
        throw Exception('Supabase not initialized. Cannot add review.');
      }

      // Validate review data
      if (!review.isValid) {
        throw Exception('Invalid review data. Rating must be 1-5 and comment cannot be empty.');
      }

      debugPrint('SupabaseService: Adding new review for shop ID ${review.shopId}...');

      // Insert the review into the 'yorumlar' table and return the created record
      final response = await client
          .from('yorumlar')
          .insert(review.toSubmissionJson())
          .select()
          .single();

      debugPrint('SupabaseService: Review created successfully');

      // Parse and return the created review
      final createdReview = Review.fromJson(response);
      debugPrint('SupabaseService: Created review with ID ${createdReview.id}');

      return createdReview;

    } catch (e) {
      // Log the error for debugging purposes
      debugPrint('SupabaseService: Error adding review: $e');

      // Re-throw the exception to allow the UI to handle it appropriately
      throw Exception('Failed to add review: $e');
    }
  }

  /// Uploads user profile image to Supabase Storage
  ///
  /// Returns the public URL of the uploaded image, or null if upload fails.
  /// This method handles image upload to the 'profile_pictures' storage bucket
  /// with comprehensive error handling and graceful degradation.
  ///
  /// [userId] The unique identifier for the user
  /// [imageFile] The XFile containing the image to upload
  ///
  /// Returns the public URL string if successful, null if failed.
  ///
  /// Example usage:
  /// ```dart
  /// final supabaseService = SupabaseService();
  /// final imageUrl = await supabaseService.uploadProfileImage(userId, imageFile);
  /// if (imageUrl != null) {
  ///   print('Image uploaded successfully: $imageUrl');
  /// }
  /// ```
  Future<String?> uploadProfileImage(String userId, XFile imageFile) async {
    try {
      // Check if Supabase is initialized before attempting upload
      if (!isInitialized) {
        debugPrint('SupabaseService: Supabase not initialized, cannot upload image');
        return null;
      }

      debugPrint('SupabaseService: Starting profile image upload for user: $userId');

      // Extract file extension from the image file path
      final String fileName = imageFile.path.split('/').last;
      final String extension = fileName.split('.').last.toLowerCase();

      // Validate file extension
      const allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
      if (!allowedExtensions.contains(extension)) {
        debugPrint('SupabaseService: Invalid file extension: $extension');
        throw Exception('Unsupported file format. Please use JPG, PNG, or WebP.');
      }

      // Create file path for storage
      final String filePath = 'public/profile_pictures/$userId.$extension';
      debugPrint('SupabaseService: Uploading to path: $filePath');

      // Read file bytes
      final Uint8List fileBytes = await imageFile.readAsBytes();
      debugPrint('SupabaseService: File size: ${fileBytes.length} bytes');

      // Upload file to Supabase Storage
      await client.storage
          .from('profile_pictures')
          .uploadBinary(
            filePath,
            fileBytes,
            fileOptions: FileOptions(
              contentType: 'image/$extension',
              upsert: true, // Allow overwriting existing files
            ),
          );

      debugPrint('SupabaseService: File uploaded successfully');

      // Get public URL for the uploaded file
      final String publicUrl = client.storage
          .from('profile_pictures')
          .getPublicUrl(filePath);

      debugPrint('SupabaseService: Public URL generated: $publicUrl');
      return publicUrl;

    } catch (e) {
      debugPrint('SupabaseService: Error uploading profile image: $e');

      // Return null to allow graceful degradation
      // The profile can still be saved without an image
      return null;
    }
  }

  /// Saves or updates user profile in Supabase 'profiles' table
  ///
  /// Uses upsert operation to handle both insert and update cases.
  /// This method creates or updates a complete user profile with all
  /// onboarding information including coffee preferences.
  ///
  /// [userId] The unique identifier for the user (required)
  /// [fullName] The user's full name (optional)
  /// [avatarUrl] The profile image URL from storage (optional)
  /// [coffeePreferences] Structured coffee preferences as Map (optional)
  /// [onboardingCompleted] Whether onboarding is completed (default: false)
  ///
  /// Throws [Exception] if the operation fails.
  ///
  /// Example usage:
  /// ```dart
  /// final supabaseService = SupabaseService();
  /// await supabaseService.updateUserProfile(
  ///   userId: 'user-123',
  ///   fullName: 'John Doe',
  ///   avatarUrl: 'https://...',
  ///   coffeePreferences: {
  ///     'favorite_types': ['Espresso', 'Latte'],
  ///     'roast_level': 'Medium',
  ///     'milk_options': ['Whole Milk'],
  ///     'sweetened': false,
  ///   },
  ///   onboardingCompleted: true,
  /// );
  /// ```
  Future<void> updateUserProfile({
    required String userId,
    String? fullName,
    String? avatarUrl,
    Map<String, dynamic>? coffeePreferences,
    bool onboardingCompleted = false,
  }) async {
    try {
      // Check if Supabase is initialized before attempting database operation
      if (!isInitialized) {
        debugPrint('SupabaseService: Supabase not initialized, cannot update profile');
        throw Exception('Database service is not available. Please try again later.');
      }

      debugPrint('SupabaseService: Updating profile for user: $userId');
      debugPrint('SupabaseService: Full name: $fullName');
      debugPrint('SupabaseService: Avatar URL: $avatarUrl');
      debugPrint('SupabaseService: Coffee preferences: $coffeePreferences');
      debugPrint('SupabaseService: Onboarding completed: $onboardingCompleted');

      // Prepare the data for upsert operation
      // Use user_id field for UUID and let id be auto-incrementing
      final Map<String, dynamic> profileData = {
        'user_id': userId,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Add optional fields if provided
      if (fullName != null) {
        profileData['full_name'] = fullName.trim();
      }

      if (avatarUrl != null) {
        profileData['avatar_url'] = avatarUrl;
      }

      if (coffeePreferences != null) {
        profileData['coffee_preferences'] = coffeePreferences;
      }

      profileData['onboarding_completed'] = onboardingCompleted;

      // Perform upsert operation (insert or update)
      // Use user_id as the conflict resolution column
      await client
          .from('profiles')
          .upsert(profileData, onConflict: 'user_id');

      debugPrint('SupabaseService: Profile updated successfully');

    } catch (e) {
      debugPrint('SupabaseService: Error updating user profile: $e');

      // Re-throw with user-friendly message
      if (e.toString().contains('relation "public.profiles" does not exist')) {
        throw Exception('Profil tablosu bulunamadı. Lütfen daha sonra tekrar deneyin.');
      } else if (e.toString().contains('network')) {
        throw Exception('İnternet bağlantısı sorunu. Lütfen bağlantınızı kontrol edin.');
      } else {
        throw Exception('Profil kaydedilirken hata oluştu. Lütfen tekrar deneyin.');
      }
    }
  }

  /// Fetches complete user profile data from Supabase 'profiles' table
  ///
  /// Returns the complete profile data as a Map if found, null if no profile exists.
  /// This method fetches all relevant profile fields including coffee preferences
  /// for display in the ProfileScreen.
  ///
  /// [userId] The unique identifier for the user (UUID from Supabase Auth)
  ///
  /// Returns a Map containing profile data or null if not found.
  ///
  /// Example usage:
  /// ```dart
  /// final supabaseService = SupabaseService();
  /// final profileData = await supabaseService.getUserProfile(userId);
  /// if (profileData != null) {
  ///   print('User name: ${profileData['full_name']}');
  ///   print('Coffee preferences: ${profileData['coffee_preferences']}');
  /// }
  /// ```
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      // Check if Supabase is initialized before making the request
      if (!isInitialized) {
        debugPrint('SupabaseService: Supabase not initialized, cannot fetch user profile');
        return null;
      }

      debugPrint('SupabaseService: Fetching user profile for user: $userId');

      // Fetch complete profile data from the 'profiles' table
      // Use user_id field to match UUID (not the auto-incrementing id field)
      final response = await client
          .from('profiles')
          .select('full_name, avatar_url, coffee_preferences, onboarding_completed, created_at, updated_at')
          .eq('user_id', userId)
          .maybeSingle();

      if (response != null) {
        debugPrint('SupabaseService: User profile found');
        debugPrint('SupabaseService: Profile data: $response');
        return response;
      } else {
        debugPrint('SupabaseService: No profile found for user: $userId');
        return null;
      }

    } catch (e) {
      debugPrint('SupabaseService: Error fetching user profile: $e');

      // Return null to allow graceful degradation
      // The ProfileScreen can handle missing profile data appropriately
      return null;
    }
  }
}
