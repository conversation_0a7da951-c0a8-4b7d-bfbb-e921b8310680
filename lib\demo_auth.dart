import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'screens/auth_screen.dart';

/// Demo app to showcase the AuthScreen independently with Supabase integration
///
/// This file can be used to test the AuthScreen in isolation
/// by running: flutter run lib/demo_auth.dart
///
/// Note: This demo will work with placeholder Supabase credentials,
/// showing graceful degradation when Supabase is not properly configured.
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Supabase with placeholder credentials for demo
    await Supabase.initialize(
      url: 'YOUR_SUPABASE_URL',
      anonKey: 'YOUR_SUPABASE_ANON_KEY',
    );
  } catch (e) {
    // Demo will continue to work even without proper Supabase configuration
    debugPrint('Demo running without Supabase configuration: $e');
  }

  runApp(const AuthScreenDemo());
}

class AuthScreenDemo extends StatelessWidget {
  const AuthScreenDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SipTracker Auth Demo',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const AuthScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
