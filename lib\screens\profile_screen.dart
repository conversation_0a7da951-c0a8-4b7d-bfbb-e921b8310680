import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/supabase_service.dart';
import '../services/theme_notifier.dart';
import '../widgets/auth_gate.dart';
import '../widgets/coffee_wave_progress_indicator.dart';
import '../models/user_profile.dart';

/// Profile screen with user account management and sign-out functionality
///
/// This screen displays user profile information and provides sign-out capability.
/// The sign-out functionality integrates with the global authentication state
/// management system for automatic navigation handling.
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  // Supabase service instance for authentication operations
  final SupabaseService _supabaseService = SupabaseService();

  // Loading state for sign-out operation
  bool _isSigningOut = false;

  // State variables for profile data
  bool _isLoading = true;
  UserProfile? _userProfile;
  String? _errorMessage;

  // Coffee theme colors
  static const Color _coffeeColor = Color(0xFF6F4E37);
  static const Color _creamColor = Color(0xFFF5F5DC);

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  /// Loads user profile data from Supabase
  Future<void> _loadUserProfile() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Get current user ID
      final userId = _supabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('Kullanıcı bilgisi bulunamadı. Lütfen tekrar giriş yapın.');
      }

      // Fetch profile data
      final profileData = await _supabaseService.getUserProfile(userId);

      if (mounted) {
        setState(() {
          if (profileData != null) {
            _userProfile = UserProfile.fromJson(profileData);
          } else {
            _userProfile = null;
          }
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().replaceAll('Exception: ', '');
          _isLoading = false;
        });
      }
    }
  }

  /// Handles user sign-out with comprehensive error handling
  ///
  /// This method signs out the current user using Supabase authentication.
  /// The global authentication state listener will automatically handle
  /// navigation to the AuthScreen when sign-out is successful.
  Future<void> _signOut() async {
    // Check if Supabase is initialized
    if (!_supabaseService.isInitialized) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Authentication service is not available.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    setState(() {
      _isSigningOut = true;
    });

    try {
      // Sign out using Supabase service
      await _supabaseService.signOut();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Signed out successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Note: Navigation to AuthScreen is handled automatically by the
      // authentication state listener in main.dart
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Failed to sign out. Please try again.';

        // Extract user-friendly error messages
        final errorString = e.toString().toLowerCase();
        if (errorString.contains('network') || errorString.contains('connection')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSigningOut = false;
        });
      }
    }
  }

  /// Builds the theme mode selector widget
  ///
  /// Creates a column of radio list tiles for selecting between light, dark, and system themes.
  /// Uses Material 3 design with proper spacing and visual feedback.
  Widget _buildThemeModeSelector(ThemeNotifier themeNotifier) {
    return Column(
      children: [
        RadioListTile<ThemeModeOption>(
          title: const Text('Aydınlık'),
          subtitle: const Text('Her zaman açık tema kullan'),
          value: ThemeModeOption.light,
          groupValue: themeNotifier.currentThemeMode,
          onChanged: (value) {
            if (value != null) {
              themeNotifier.setThemeMode(value);
            }
          },
          contentPadding: EdgeInsets.zero,
        ),
        RadioListTile<ThemeModeOption>(
          title: const Text('Karanlık'),
          subtitle: const Text('Her zaman koyu tema kullan'),
          value: ThemeModeOption.dark,
          groupValue: themeNotifier.currentThemeMode,
          onChanged: (value) {
            if (value != null) {
              themeNotifier.setThemeMode(value);
            }
          },
          contentPadding: EdgeInsets.zero,
        ),
        RadioListTile<ThemeModeOption>(
          title: const Text('Sistem'),
          subtitle: const Text('Cihaz ayarlarını takip et'),
          value: ThemeModeOption.system,
          groupValue: themeNotifier.currentThemeMode,
          onChanged: (value) {
            if (value != null) {
              themeNotifier.setThemeMode(value);
            }
          },
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  /// Builds the accent color selector widget
  ///
  /// Creates a horizontal row of color picker circles with visual selection indicators.
  /// Each color is displayed as a CircleAvatar with a border for the selected color.
  Widget _buildAccentColorSelector(ThemeNotifier themeNotifier) {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: accentColors.map((color) {
        final isSelected = themeNotifier.currentAccentColor == color;

        return GestureDetector(
          onTap: () {
            themeNotifier.setAccentColor(color);
          },
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
                width: 3,
              ),
            ),
            child: CircleAvatar(
              backgroundColor: color,
              radius: 20,
              child: isSelected
                ? Icon(
                    Icons.check,
                    color: _getContrastingColor(color),
                    size: 20,
                  )
                : null,
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Helper method to get a contrasting color for the check icon
  ///
  /// Returns white for dark colors and black for light colors to ensure visibility.
  Color _getContrastingColor(Color color) {
    // Calculate luminance to determine if the color is light or dark
    final red = (color.r * 255.0).round() & 0xff;
    final green = (color.g * 255.0).round() & 0xff;
    final blue = (color.b * 255.0).round() & 0xff;
    final luminance = (0.299 * red + 0.587 * green + 0.114 * blue) / 255;
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _creamColor.withValues(alpha: 0.1),
      appBar: AppBar(
        title: const Text('Profil'),
        backgroundColor: _coffeeColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CoffeeWaveProgressIndicator(size: 80.0),
                  SizedBox(height: 16),
                  Text('Profil yükleniyor...'),
                ],
              ),
            )
          : _errorMessage != null
              ? _buildErrorState()
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildProfileHeader(),
                      const SizedBox(height: 24),
                      if (_userProfile?.hasCoffeePreferences == true) ...[
                        _buildCoffeePreferencesSection(),
                        const SizedBox(height: 24),
                      ],
                      _buildThemeSettingsSection(),
                      const SizedBox(height: 24),
                      _buildSignOutButton(),
                    ],
                  ),
                ),
    );
  }

  /// Builds error state with retry functionality
  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Profil Yüklenemedi',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: _coffeeColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Bilinmeyen bir hata oluştu',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadUserProfile,
              icon: const Icon(Icons.refresh),
              label: const Text('Tekrar Dene'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _coffeeColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the profile header section with avatar and user info
  Widget _buildProfileHeader() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              _coffeeColor.withValues(alpha: 0.1),
              _creamColor.withValues(alpha: 0.3),
            ],
          ),
        ),
        child: Column(
          children: [
            _buildProfileAvatar(),
            const SizedBox(height: 16),
            _buildUserInfo(),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0);
  }

  /// Builds the profile avatar with coffee-themed styling
  Widget _buildProfileAvatar() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: _coffeeColor,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: _coffeeColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipOval(
        child: _userProfile?.avatarUrl != null
            ? Image.network(
                _userProfile!.avatarUrl!,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: _creamColor.withValues(alpha: 0.3),
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return _buildAvatarFallback();
                },
              )
            : _buildAvatarFallback(),
      ),
    );
  }

  /// Builds avatar fallback with user initials or coffee icon
  Widget _buildAvatarFallback() {
    return Container(
      color: _coffeeColor.withValues(alpha: 0.1),
      child: Center(
        child: _userProfile?.hasBasicInfo == true
            ? Text(
                _userProfile!.initials,
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: _coffeeColor,
                ),
              )
            : Icon(
                Icons.local_cafe,
                size: 48,
                color: _coffeeColor,
              ),
      ),
    );
  }

  /// Builds user information section
  Widget _buildUserInfo() {
    return ValueListenableBuilder<User?>(
      valueListenable: currentUser,
      builder: (context, user, child) {
        return Column(
          children: [
            Text(
              _userProfile?.fullName ?? 'Kullanıcı',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: _coffeeColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            if (user?.email != null)
              Text(
                user!.email!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            if (user?.emailConfirmedAt != null) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.verified,
                    size: 16,
                    color: Colors.green[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Doğrulanmış',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        );
      },
    );
  }

  /// Builds the coffee preferences section
  Widget _buildCoffeePreferencesSection() {
    final preferences = _userProfile?.coffeePreferences;
    if (preferences == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.coffee,
                  color: _coffeeColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Kahve Zevklerim',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _coffeeColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Favorite coffee types
            if (preferences.favoriteTypes.isNotEmpty) ...[
              _buildPreferenceSubsection(
                'En Sevdiğim Kahve Türleri',
                Icons.local_cafe,
                preferences.favoriteTypes,
              ),
              const SizedBox(height: 16),
            ],

            // Roast level
            if (preferences.roastLevel != null) ...[
              _buildSinglePreference(
                'Kavurma Derecesi',
                Icons.grain,
                preferences.roastLevel!,
              ),
              const SizedBox(height: 16),
            ],

            // Milk preferences
            if (preferences.milkOptions.isNotEmpty) ...[
              _buildPreferenceSubsection(
                'Süt Tercihleri',
                Icons.water_drop,
                preferences.milkOptions,
              ),
              const SizedBox(height: 16),
            ],

            // Sweetener preference
            _buildSinglePreference(
              'Şeker Tercihi',
              preferences.sweetened ? Icons.add_circle : Icons.remove_circle,
              preferences.sweetened ? 'Şekerli' : 'Şekersiz',
            ),
          ],
        ),
      ),
    ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }

  /// Builds a preference subsection with multiple items
  Widget _buildPreferenceSubsection(String title, IconData icon, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18, color: _coffeeColor),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: _coffeeColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: items.map((item) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _coffeeColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _coffeeColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Text(
                item,
                style: TextStyle(
                  color: _coffeeColor,
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Builds a single preference item
  Widget _buildSinglePreference(String title, IconData icon, String value) {
    return Row(
      children: [
        Icon(icon, size: 18, color: _coffeeColor),
        const SizedBox(width: 8),
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: _coffeeColor,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the theme settings section
  Widget _buildThemeSettingsSection() {
    return Consumer<ThemeNotifier>(
      builder: (context, themeNotifier, child) {
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.palette,
                      color: _coffeeColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Görünüm Ayarları',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: _coffeeColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Theme mode selection
                Text(
                  'Tema',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: _coffeeColor,
                  ),
                ),
                const SizedBox(height: 8),
                _buildThemeModeSelector(themeNotifier),

                const SizedBox(height: 20),

                // Accent color selection
                Text(
                  'Vurgu Rengi',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: _coffeeColor,
                  ),
                ),
                const SizedBox(height: 8),
                _buildAccentColorSelector(themeNotifier),
              ],
            ),
          ),
        ).animate(delay: 400.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
      },
    );
  }

  /// Builds the sign out button
  Widget _buildSignOutButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isSigningOut ? null : _signOut,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 4,
        ),
        child: _isSigningOut
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.logout),
                  SizedBox(width: 8),
                  Text(
                    'Çıkış Yap',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
      ),
    ).animate(delay: 600.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }
}
